# Conversation Privacy & User Isolation

## Problem

Previously, all users shared the same conversation history because the application used a hardcoded `contextKey="tambo-template"` for all users. This meant:

- ❌ All users could see each other's "Previous chat" conversations
- ❌ No privacy between different users
- ❌ Anonymous users shared the same conversation thread

## Solution

We implemented user-specific context keys that ensure complete conversation isolation:

### 1. Wallet-Based User Identification

For users with connected wallets, we use their wallet address as a unique identifier:

```typescript
// MetaMask user
tambo-template-******************************************

// CDP wallet user  
tambo-template-******************************************

// Smart wallet user
tambo-template-******************************************
```

### 2. Anonymous Session Isolation

For anonymous users (no wallet connected), we generate unique session IDs:

```typescript
// Each anonymous user gets a unique session ID
tambo-template-anonymous-355830c78b681613071656f98ea90abd
tambo-template-anonymous-4dbbbd4601fb84294822fc302f3da270
```

#### Anonymous Session Behavior:
- **Same browser tab/window**: Conversations persist during the session
- **New tab/window**: Gets a new unique session ID
- **Browser restart**: Gets a new unique session ID
- **Different browsers**: Each gets its own unique session ID

### 3. Implementation Details

#### Core Function (`src/lib/user-context.ts`)

```typescript
export function generateUserContextKey(
  paymentContext: PaymentContext | null,
  isWalletReady: boolean,
  baseKey: string = 'tambo-template'
): string
```

#### Session Storage for Anonymous Users

- Uses `sessionStorage` to persist anonymous session IDs within the same browser session
- Automatically generates new IDs for new sessions
- Falls back gracefully for server-side rendering

#### Crypto-Secure Random Generation

- Uses `window.crypto.getRandomValues()` for cryptographically secure random IDs
- Falls back to `Math.random()` for older browsers or server-side

## Privacy Benefits

### ✅ Complete User Isolation
- Each user has their own private conversation history
- No cross-contamination between users
- Wallet users maintain persistent history across sessions

### ✅ Anonymous User Privacy  
- Each anonymous session is completely isolated
- No shared conversation threads
- Fresh start for each new browser session

### ✅ Multi-Wallet Support
- Works with MetaMask, CDP wallets, and Smart wallets
- Consistent behavior across all wallet types
- Automatic address normalization (lowercase)

### ✅ Session Management
- Anonymous users: Session-based isolation
- Wallet users: Persistent cross-session history
- Graceful handling of wallet connection/disconnection

## Usage Examples

### Wallet User Flow
1. User connects MetaMask with address `0x742d35...`
2. Gets context key: `tambo-template-******************************************`
3. All conversations are saved under this key
4. User can disconnect/reconnect and maintain same history

### Anonymous User Flow
1. User visits site without connecting wallet
2. Gets unique session ID: `355830c78b681613071656f98ea90abd`
3. Gets context key: `tambo-template-anonymous-355830c78b681613071656f98ea90abd`
4. Conversations persist during browser session
5. New tab/browser restart = new session ID = fresh conversations

### Switching Between Modes
- **Anonymous → Wallet**: Gets new wallet-based context key, previous anonymous conversations remain isolated
- **Wallet → Anonymous**: Gets new anonymous session ID, wallet conversations remain under wallet key
- **Different Wallets**: Each wallet gets its own context key

## Testing

Comprehensive test suite in `src/lib/__tests__/user-context.test.ts` covers:
- ✅ Wallet user isolation
- ✅ Anonymous session generation
- ✅ Session persistence
- ✅ Cross-session isolation
- ✅ Server-side rendering compatibility
- ✅ Edge cases and fallbacks

## Security Considerations

1. **No PII in Context Keys**: Only wallet addresses (public information) are used
2. **Cryptographically Secure**: Uses `crypto.getRandomValues()` for anonymous IDs
3. **Session-Only Storage**: Anonymous IDs stored in `sessionStorage`, not `localStorage`
4. **Address Normalization**: All addresses converted to lowercase for consistency

## Migration

- **Existing Users**: No impact, they'll get new user-specific context keys
- **Anonymous Users**: Previous shared conversations remain under old key, new users get isolated sessions
- **Backward Compatibility**: System gracefully handles all scenarios

This implementation ensures complete privacy and isolation while maintaining a seamless user experience across all wallet types and usage patterns.
