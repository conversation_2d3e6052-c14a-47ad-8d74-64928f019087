import { PaymentContext } from '@/lib/payment';

/**
 * Generates a unique context key for Tambo threads based on user's wallet address
 * This ensures each user has their own isolated conversation history
 */
export function generateUserContextKey(
  paymentContext: PaymentContext | null,
  isWalletReady: boolean,
  baseKey: string = 'tambo-template'
): string {
  if (!isWalletReady || !paymentContext) {
    return `${baseKey}-anonymous`; // Fallback for users without wallet
  }
  
  // Use wallet address as unique identifier
  const userAddress = paymentContext.userAddress || 
                     paymentContext.walletInfo?.address || 
                     paymentContext.smartWalletInfo?.address;
  
  if (userAddress) {
    return `${baseKey}-${userAddress.toLowerCase()}`;
  }
  
  return `${baseKey}-anonymous`;
}

/**
 * Hook to get the current user's context key
 */
export function useUserContextKey(baseKey: string = 'tambo-template'): string {
  // This would need to be implemented with the wallet context
  // For now, we'll keep the logic in the component
  return baseKey;
}
