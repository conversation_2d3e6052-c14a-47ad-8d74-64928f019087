import { generateUserContextKey } from '../user-context';
import { PaymentContext } from '../payment';

describe('generateUserContextKey', () => {
  it('should return anonymous context when wallet is not ready', () => {
    const result = generateUserContextKey(null, false);
    expect(result).toBe('tambo-template-anonymous');
  });

  it('should return anonymous context when paymentContext is null', () => {
    const result = generateUserContextKey(null, true);
    expect(result).toBe('tambo-template-anonymous');
  });

  it('should generate unique context key for MetaMask user', () => {
    const paymentContext: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };
    
    const result = generateUserContextKey(paymentContext, true);
    expect(result).toBe('tambo-template-******************************************');
  });

  it('should generate unique context key for CDP wallet user', () => {
    const paymentContext: PaymentContext = {
      walletType: 'cdp',
      walletInfo: {
        id: 'test-wallet',
        address: '******************************************',
        network: 'base-sepolia',
      },
    };
    
    const result = generateUserContextKey(paymentContext, true);
    expect(result).toBe('tambo-template-******************************************');
  });

  it('should generate unique context key for Smart wallet user', () => {
    const paymentContext: PaymentContext = {
      walletType: 'cdp',
      smartWalletInfo: {
        address: '******************************************',
        network: 'base-sepolia',
      },
    };
    
    const result = generateUserContextKey(paymentContext, true);
    expect(result).toBe('tambo-template-******************************************');
  });

  it('should use custom base key', () => {
    const paymentContext: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };
    
    const result = generateUserContextKey(paymentContext, true, 'custom-app');
    expect(result).toBe('custom-app-******************************************');
  });

  it('should return anonymous when no address is available', () => {
    const paymentContext: PaymentContext = {
      walletType: 'cdp',
      // No address fields provided
    };
    
    const result = generateUserContextKey(paymentContext, true);
    expect(result).toBe('tambo-template-anonymous');
  });

  it('should generate different keys for different users', () => {
    const user1Context: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };
    
    const user2Context: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };
    
    const key1 = generateUserContextKey(user1Context, true);
    const key2 = generateUserContextKey(user2Context, true);
    
    expect(key1).not.toBe(key2);
    expect(key1).toBe('tambo-template-******************************************');
    expect(key2).toBe('tambo-template-******************************************');
  });
});
