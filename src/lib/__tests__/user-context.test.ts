import { generateUserContextKey } from '../user-context';
import { PaymentContext } from '../payment';

// Mock sessionStorage for testing
const mockSessionStorage = {
  store: {} as Record<string, string>,
  getItem: jest.fn((key: string) => mockSessionStorage.store[key] || null),
  setItem: jest.fn((key: string, value: string) => {
    mockSessionStorage.store[key] = value;
  }),
  clear: jest.fn(() => {
    mockSessionStorage.store = {};
  }),
};

// Mock window.crypto for testing
const mockCrypto = {
  getRandomValues: jest.fn((array: Uint8Array) => {
    // Fill with predictable values for testing
    for (let i = 0; i < array.length; i++) {
      array[i] = i + 1;
    }
    return array;
  }),
};

describe('generateUserContextKey', () => {
  beforeEach(() => {
    // Reset mocks
    mockSessionStorage.clear();
    jest.clearAllMocks();

    // Mock global objects
    Object.defineProperty(global, 'window', {
      value: {
        crypto: mockCrypto,
        sessionStorage: mockSessionStorage,
      },
      writable: true,
    });
  });

  afterEach(() => {
    // Clean up
    delete (global as any).window;
  });

  it('should return unique anonymous context when wallet is not ready', () => {
    const result = generateUserContextKey(null, false);
    expect(result).toMatch(/^tambo-template-anonymous-[a-f0-9]+$/);
  });

  it('should return unique anonymous context when paymentContext is null', () => {
    const result = generateUserContextKey(null, true);
    expect(result).toMatch(/^tambo-template-anonymous-[a-f0-9]+$/);
  });

  it('should return same anonymous context for same session', () => {
    const result1 = generateUserContextKey(null, false);
    const result2 = generateUserContextKey(null, false);
    expect(result1).toBe(result2);
    expect(mockSessionStorage.getItem).toHaveBeenCalled();
    expect(mockSessionStorage.setItem).toHaveBeenCalledTimes(1);
  });

  it('should generate unique context key for MetaMask user', () => {
    const paymentContext: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };
    
    const result = generateUserContextKey(paymentContext, true);
    expect(result).toBe('tambo-template-******************************************');
  });

  it('should generate unique context key for CDP wallet user', () => {
    const paymentContext: PaymentContext = {
      walletType: 'cdp',
      walletInfo: {
        id: 'test-wallet',
        address: '******************************************',
        network: 'base-sepolia',
      },
    };
    
    const result = generateUserContextKey(paymentContext, true);
    expect(result).toBe('tambo-template-******************************************');
  });

  it('should generate unique context key for Smart wallet user', () => {
    const paymentContext: PaymentContext = {
      walletType: 'cdp',
      smartWalletInfo: {
        address: '******************************************',
        network: 'base-sepolia',
      },
    };
    
    const result = generateUserContextKey(paymentContext, true);
    expect(result).toBe('tambo-template-******************************************');
  });

  it('should use custom base key', () => {
    const paymentContext: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };
    
    const result = generateUserContextKey(paymentContext, true, 'custom-app');
    expect(result).toBe('custom-app-******************************************');
  });

  it('should return unique anonymous when no address is available', () => {
    const paymentContext: PaymentContext = {
      walletType: 'cdp',
      // No address fields provided
    };

    const result = generateUserContextKey(paymentContext, true);
    expect(result).toMatch(/^tambo-template-anonymous-[a-f0-9]+$/);
  });

  it('should generate different keys for different users', () => {
    const user1Context: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };

    const user2Context: PaymentContext = {
      walletType: 'metamask',
      userAddress: '******************************************',
    };

    const key1 = generateUserContextKey(user1Context, true);
    const key2 = generateUserContextKey(user2Context, true);

    expect(key1).not.toBe(key2);
    expect(key1).toBe('tambo-template-******************************************');
    expect(key2).toBe('tambo-template-******************************************');
  });

  it('should generate different anonymous keys for different sessions', () => {
    // First session
    const result1 = generateUserContextKey(null, false);

    // Clear session storage to simulate new session
    mockSessionStorage.clear();

    // Second session
    const result2 = generateUserContextKey(null, false);

    expect(result1).not.toBe(result2);
    expect(result1).toMatch(/^tambo-template-anonymous-[a-f0-9]+$/);
    expect(result2).toMatch(/^tambo-template-anonymous-[a-f0-9]+$/);
  });

  it('should handle server-side rendering gracefully', () => {
    // Remove window object to simulate SSR
    delete (global as any).window;

    const result = generateUserContextKey(null, false);
    expect(result).toMatch(/^tambo-template-anonymous-[a-z0-9]+$/);
  });
});
