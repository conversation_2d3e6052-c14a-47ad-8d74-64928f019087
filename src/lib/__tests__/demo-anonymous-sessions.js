/**
 * <PERSON><PERSON> script to show how anonymous session IDs work
 * Run with: node src/lib/__tests__/demo-anonymous-sessions.js
 */

// Mock browser environment
global.window = {
  crypto: {
    getRandomValues: (array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    }
  },
  sessionStorage: {
    store: {},
    getItem: function(key) {
      return this.store[key] || null;
    },
    setItem: function(key, value) {
      this.store[key] = value;
    },
    clear: function() {
      this.store = {};
    }
  }
};

// Import our function (simplified version for demo)
function generateAnonymousSessionId() {
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const array = new Uint8Array(16);
    window.crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
  
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

function getAnonymousSessionId() {
  const storageKey = 'tambo-anonymous-session-id';
  let sessionId = window.sessionStorage.getItem(storageKey);
  
  if (!sessionId) {
    sessionId = generateAnonymousSessionId();
    window.sessionStorage.setItem(storageKey, sessionId);
    console.log('🆕 Generated new anonymous session ID:', sessionId);
  } else {
    console.log('♻️  Reusing existing session ID:', sessionId);
  }
  
  return sessionId;
}

function generateUserContextKey(paymentContext, isWalletReady, baseKey = 'tambo-template') {
  if (!isWalletReady || !paymentContext) {
    const anonymousId = getAnonymousSessionId();
    return `${baseKey}-anonymous-${anonymousId}`;
  }
  
  const userAddress = paymentContext.userAddress || 
                     paymentContext.walletInfo?.address || 
                     paymentContext.smartWalletInfo?.address;
  
  if (userAddress) {
    return `${baseKey}-${userAddress.toLowerCase()}`;
  }
  
  const anonymousId = getAnonymousSessionId();
  return `${baseKey}-anonymous-${anonymousId}`;
}

// Demo scenarios
console.log('🔍 Demo: Anonymous Session Behavior\n');

console.log('--- Scenario 1: Multiple anonymous users in same session ---');
const key1 = generateUserContextKey(null, false);
const key2 = generateUserContextKey(null, false);
console.log('First call:', key1);
console.log('Second call:', key2);
console.log('Same session?', key1 === key2 ? '✅ Yes' : '❌ No');

console.log('\n--- Scenario 2: New browser session (cleared storage) ---');
window.sessionStorage.clear();
const key3 = generateUserContextKey(null, false);
console.log('After clearing storage:', key3);
console.log('Different from previous?', key3 !== key1 ? '✅ Yes' : '❌ No');

console.log('\n--- Scenario 3: Wallet user vs Anonymous user ---');
const walletUser = {
  walletType: 'metamask',
  userAddress: '******************************************'
};
const walletKey = generateUserContextKey(walletUser, true);
const anonKey = generateUserContextKey(null, false);

console.log('Wallet user key:', walletKey);
console.log('Anonymous user key:', anonKey);
console.log('Different keys?', walletKey !== anonKey ? '✅ Yes' : '❌ No');

console.log('\n--- Scenario 4: Different wallet users ---');
const user1 = {
  walletType: 'metamask',
  userAddress: '******************************************'
};
const user2 = {
  walletType: 'cdp',
  walletInfo: {
    address: '******************************************'
  }
};

const user1Key = generateUserContextKey(user1, true);
const user2Key = generateUserContextKey(user2, true);

console.log('User 1 key:', user1Key);
console.log('User 2 key:', user2Key);
console.log('Different keys?', user1Key !== user2Key ? '✅ Yes' : '❌ No');

console.log('\n🎉 All scenarios demonstrate proper isolation!');
