@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 235 12% 21%;
    --primary-foreground: 0 0% 98%;
    --secondary: 218 11% 46%;
    --secondary-foreground: 0 0% 100%;
    --muted: 217 14% 90%;
    --muted-foreground: 217 14% 68%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 207 22% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 7.1%; /* #121212 */
    --foreground: 0 0% 98%;
    --card: 0 0% 9.4%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 9.4%;
    --popover-foreground: 0 0% 98%;
    --primary: 221 83% 70%;
    --primary-foreground: 0 0% 7.1%;
    --secondary: 0 0% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15.9%;
    --muted-foreground: 0 0% 64.9%;
    --accent: 0 0% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 15.9%;
    --input: 0 0% 15.9%;
    --ring: 221 83% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  }
}

/* Dark mode scrollbar styling */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.dark ::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}
.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--secondary));
  border-radius: 4px;
}
.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary-foreground));
}

/* Highlight.js Dark Theme Overrides */
.dark .hljs { color: #c9d1d9; background: #0d1117; }
.dark .hljs-comment, .dark .hljs-quote { color: #8b949e; font-style: italic; }
.dark .hljs-keyword, .dark .hljs-selector-tag, .dark .hljs-literal, .dark .hljs-meta { color: #ff7b72; }
.dark .hljs-string, .dark .hljs-regexp, .dark .hljs-attr, .dark .hljs-symbol { color: #a5d6ff; }
.dark .hljs-number { color: #79c0ff; }
.dark .hljs-variable, .dark .hljs-template-variable, .dark .hljs-title.function_ { color: #ffa657; }
.dark .hljs-title.class_, .dark .hljs-type { color: #ffa657; }
.dark .hljs-built_in { color: #79c0ff; }
